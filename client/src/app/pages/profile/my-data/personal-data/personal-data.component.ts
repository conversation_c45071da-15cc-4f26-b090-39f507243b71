import {FormBuilder, ReactiveFormsModule} from "@angular/forms";
import {ProfileService} from "@/services/profile.service";
import {Component, inject, ViewChild, ElementRef} from '@angular/core';
import {Router} from "@angular/router";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import { ToasterService } from "@/services/toaster.service";
import {FileService} from "@/services/file.service";
import {environment} from "@/env/environment";
import { AuthService } from "@/services/auth.service";

@Component({
  selector: 'PersonalData',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NgOptimizedImage
  ],
  templateUrl: './personal-data.component.html',
  styleUrl: './personal-data.component.scss'
})
export class PersonalDataComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  toasterService = inject(ToasterService);
  fileService = inject(FileService);
  router = inject(Router);
  authService = inject(AuthService);
  
  form: any = this.fb.group({
    id: [null],
    firstName: [null],
    lastName: [null],
    middleName: [null],
    spiritualName: [null],
    email: [null],
    phone: [null],
    telegram: [null],
    avatar: [null]
  })

  @ViewChild('fileInput') fileInput!: ElementRef;

  ngOnInit() {
    this.init();
  }

  init() {
    this.form = this.fb.group({
      id: [null],
      firstName: [null],
      lastName: [null],
      middleName: [null],
      spiritualName: [null],
      email: [null],
      phone: [null],
      telegram: [null],
      avatar: [null]
    });

    if(this.profileService?.profile) {
      this.form.patchValue(this.profileService.profile as any)
    } else {
      this.profileService.getProfile().subscribe(res => {
        this.form.patchValue(res)
      })
    }
  }

  uploadAvatar(e: Event) {
    const files = (e.target as HTMLInputElement).files!
    this.fileService.upload(files, 'avatar').subscribe((res: any) => {
      this.form.patchValue({ avatar: res[0] });
    })
  }

  onSubmit() {
    this.profileService.update(this.form).subscribe({
      next: () => {
        this.profileService.getProfile().subscribe(()=>{
          this.init();
        });
        this.toasterService.showToast('Профиль успешно обновлен!', 'success', 'bottom-middle', 3000);
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
    return false
  }

  logout() {
    this.authService.logout();
  }

  triggerFileInput(): void {
    if (this.fileInput?.nativeElement)
      this.fileInput.nativeElement.click();
  }

  protected readonly environment = environment;
}
